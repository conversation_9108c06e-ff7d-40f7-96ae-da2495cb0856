project,issuetype,summary,description,assignee,priority,labels,parent
PROJ,Sub-task,Setup development environment,Configure local dev environment for the project,new.developer,Medium,"setup,environment",PROJ-123
PROJ,Sub-task,Code review checklist,Create comprehensive code review guidelines,senior.dev,Low,"documentation,process",PROJ-123
PROJ,Sub-task,Unit test coverage,Increase unit test coverage to 80%,qa.engineer,High,"testing,quality",PROJ-123
PROJ,Sub-task,API documentation,Document all REST API endpoints,tech.writer,Medium,"documentation,api",PROJ-456
PROJ,Sub-task,Error handling,Implement comprehensive error handling,backend.dev,High,"backend,error-handling",PROJ-456
PROJ,Sub-task,Logging implementation,Add structured logging throughout application,devops.engineer,Medium,"logging,monitoring",PROJ-456
