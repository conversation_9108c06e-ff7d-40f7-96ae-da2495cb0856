key,project,issuetype,summary,description,assignee,priority,labels,parent
PARENT-001,<PERSON><PERSON><PERSON>,Story,Implement User Authentication,Complete user authentication system,john.doe,High,"backend,security",
,PROJ,Sub-task,Create login API endpoint,Develop REST API for user login,jane.smith,Medium,"backend,api",PARENT-001
,PROJ,Sub-task,Implement password validation,Add password strength validation,bob.wilson,Medium,"backend,validation",PARENT-001
,PROJ,Sub-task,Create login UI,Design and implement login form,alice.brown,Low,"frontend,ui",PARENT-001
PARENT-002,PROJ,Epic,Database Migration,Migrate from MySQL to PostgreSQL,tech.lead,Critical,"database,migration",
,PROJ,Sub-task,Export existing data,Create scripts to export current data,db.admin,High,"database,scripts",PARENT-002
,PROJ,Sub-task,Setup PostgreSQL,Install and configure PostgreSQL server,sys.admin,High,"database,setup",PARENT-002
,PROJ,Sub-task,Update application config,Modify app to use <PERSON>greSQL,developer,Medium,"config,backend",PARENT-002
,PROJ,Sub-task,Performance testing,Test performance after migration,qa.tester,Medium,"testing,performance",PARENT-002
