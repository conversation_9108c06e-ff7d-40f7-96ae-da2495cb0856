# Test-JiraIssue.ps1
# Quick script to test if a Jira issue exists and what link types are available

param(
    [Parameter(Mandatory=$true)]
    [string]$PersonalAccessToken,
    
    [Parameter(Mandatory=$true)]
    [string]$IssueKey
)

$JiraUrl = "https://jira.softlayer.local"

# Create auth header with Bearer token
$headers = @{
    Authorization = "Bearer $PersonalAccessToken"
    "Content-Type" = "application/json"
}

Write-Host "Testing access to issue: $IssueKey" -ForegroundColor Cyan

# Test 1: Check if issue exists
try {
    $issueUrl = "$JiraUrl/rest/api/2/issue/$IssueKey"
    $issue = Invoke-RestMethod -Uri $issueUrl -Headers $headers -Method GET
    Write-Host "✓ Issue exists: $($issue.key) - $($issue.fields.summary)" -ForegroundColor Green
    Write-Host "  Status: $($issue.fields.status.name)" -ForegroundColor Gray
    Write-Host "  Project: $($issue.fields.project.key)" -ForegroundColor Gray
}
catch {
    Write-Host "✗ Error accessing issue $IssueKey`: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  This could mean the issue doesn't exist or you don't have permission to view it." -ForegroundColor Yellow
    return
}

Write-Host ""
Write-Host "Testing available link types..." -ForegroundColor Cyan

# Test 2: Get available link types
try {
    $linkTypesUrl = "$JiraUrl/rest/api/2/issueLinkType"
    $linkTypes = Invoke-RestMethod -Uri $linkTypesUrl -Headers $headers -Method GET
    
    Write-Host "✓ Available link types:" -ForegroundColor Green
    foreach ($linkType in $linkTypes.issueLinkTypes) {
        Write-Host "  - $($linkType.name)" -ForegroundColor Gray
        Write-Host "    Inward: $($linkType.inward)" -ForegroundColor DarkGray
        Write-Host "    Outward: $($linkType.outward)" -ForegroundColor DarkGray
        Write-Host ""
    }
}
catch {
    Write-Host "✗ Error getting link types: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Testing link creation permissions..." -ForegroundColor Cyan

# Test 3: Check permissions by trying to get current user info
try {
    $userUrl = "$JiraUrl/rest/api/2/myself"
    $user = Invoke-RestMethod -Uri $userUrl -Headers $headers -Method GET
    Write-Host "✓ Authenticated as: $($user.displayName) ($($user.name))" -ForegroundColor Green
}
catch {
    Write-Host "✗ Authentication error: $($_.Exception.Message)" -ForegroundColor Red
}
