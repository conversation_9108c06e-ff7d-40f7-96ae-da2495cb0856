# Get-SprintIds.ps1
# Helper script to find sprint IDs for use with Assign-JiraTickets.ps1

param(
    [Parameter(Mandatory=$true)]
    [string]$PersonalAccessToken,
    
    [Parameter()]
    [string]$SprintNameFilter,  # Optional filter to search for specific sprint names
    
    [Parameter()]
    [string]$BoardName,  # Optional filter to search specific board
    
    [Parameter()]
    [ValidateSet("active", "future", "closed", "all")]
    [string]$State = "all"  # Sprint state filter
)

$JiraUrl = "https://jira.softlayer.local"

# Create auth header with Bearer token
$headers = @{
    Authorization = "Bearer $PersonalAccessToken"
    "Content-Type" = "application/json"
}

# Test authentication first
try {
    $testUrl = "$JiraUrl/rest/api/2/myself"
    $response = Invoke-RestMethod -Uri $testUrl -Headers $headers -Method GET
    Write-Host "Authentication successful. Connected as: $($response.displayName)" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Searching for sprints..." -ForegroundColor Cyan
if ($SprintNameFilter) {
    Write-Host "Filter: Sprint names containing '$SprintNameFilter'" -ForegroundColor Gray
}
if ($BoardName) {
    Write-Host "Filter: Board name containing '$BoardName'" -ForegroundColor Gray
}
Write-Host "State filter: $State" -ForegroundColor Gray
Write-Host ""

$allSprints = @()

try {
    # Get all boards
    $boardsUrl = "$JiraUrl/rest/agile/1.0/board?maxResults=100"
    $boardsResult = Invoke-RestMethod -Uri $boardsUrl -Headers $headers -Method GET
    
    foreach ($board in $boardsResult.values) {
        # Skip board if BoardName filter is specified and doesn't match
        if ($BoardName -and $board.name -notlike "*$BoardName*") {
            continue
        }
        
        try {
            $states = if ($State -eq "all") { @("active", "future", "closed") } else { @($State) }
            
            foreach ($stateFilter in $states) {
                $sprintsUrl = "$JiraUrl/rest/agile/1.0/board/$($board.id)/sprint?state=$stateFilter&maxResults=100"
                $sprintsResult = Invoke-RestMethod -Uri $sprintsUrl -Headers $headers -Method GET
                
                foreach ($sprint in $sprintsResult.values) {
                    # Skip sprint if SprintNameFilter is specified and doesn't match
                    if ($SprintNameFilter -and $sprint.name -notlike "*$SprintNameFilter*") {
                        continue
                    }
                    
                    $sprintInfo = [PSCustomObject]@{
                        ID = $sprint.id
                        Name = $sprint.name
                        State = $sprint.state
                        Board = $board.name
                        BoardId = $board.id
                        StartDate = if ($sprint.startDate) { [DateTime]::Parse($sprint.startDate).ToString("yyyy-MM-dd") } else { "Not set" }
                        EndDate = if ($sprint.endDate) { [DateTime]::Parse($sprint.endDate).ToString("yyyy-MM-dd") } else { "Not set" }
                    }
                    $allSprints += $sprintInfo
                }
            }
        }
        catch {
            # Skip boards that don't support sprints or we don't have access to
            Write-Host "Warning: Could not access sprints for board '$($board.name)'" -ForegroundColor Yellow
        }
    }
}
catch {
    Write-Host "Error getting boards: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

if ($allSprints.Count -eq 0) {
    Write-Host "No sprints found matching your criteria." -ForegroundColor Yellow
    exit 0
}

# Sort by state (active first), then by name
$sortedSprints = $allSprints | Sort-Object @{Expression={
    switch ($_.State) {
        "active" { 1 }
        "future" { 2 }
        "closed" { 3 }
        default { 4 }
    }
}}, Name

Write-Host "Found $($sortedSprints.Count) sprints:" -ForegroundColor Green
Write-Host ""

# Display results in a table format
$sortedSprints | Format-Table -Property @(
    @{Name="ID"; Expression={$_.ID}; Width=8},
    @{Name="Name"; Expression={$_.Name}; Width=30},
    @{Name="State"; Expression={$_.State}; Width=8},
    @{Name="Board"; Expression={$_.Board}; Width=25},
    @{Name="Start Date"; Expression={$_.StartDate}; Width=12},
    @{Name="End Date"; Expression={$_.EndDate}; Width=12}
) -AutoSize

Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Cyan
Write-Host "# Add tickets to a specific sprint:" -ForegroundColor Gray
Write-Host ".\Assign-JiraTickets.ps1 -PersonalAccessToken 'token' -JqlQuery 'your-query' -SprintId 123" -ForegroundColor Gray
Write-Host ""
Write-Host "# Find active sprints only:" -ForegroundColor Gray
Write-Host ".\Get-SprintIds.ps1 -PersonalAccessToken 'token' -State active" -ForegroundColor Gray
Write-Host ""
Write-Host "# Find sprints with specific name:" -ForegroundColor Gray
Write-Host ".\Get-SprintIds.ps1 -PersonalAccessToken 'token' -SprintNameFilter '2024'" -ForegroundColor Gray
