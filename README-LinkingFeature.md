# Jira Ticket Linking Enhancement

This document describes the new issue linking functionality added to `Assign-JiraTickets.ps1`.

## New Parameters

### `-LinkToJql`
- **Type**: String
- **Description**: JQL query to find issues that should be linked to the tickets being processed
- **Example**: `"key = 'PROJ-123'"` or `"labels = 'dependency'"`

### `-LinkType`
- **Type**: String
- **Default**: "Relates"
- **Description**: The type of link to create between issues
- **Common Values**: "Relates", "Blocks", "Depends", "Duplicates", "Clones"

### `-LinkComment`
- **Type**: String
- **Description**: Optional comment to add when creating the link
- **Example**: "Automatically linked by assignment script"

### `-LinkAsInward`
- **Type**: Switch
- **Description**: If specified, creates inward links (target issues -> current issues)
- **Default**: Creates outward links (current issues -> target issues)

## How It Works

1. **Process Each Issue**: For each issue being assigned, the script:
   - Assigns the issue to the specified user
   - Adds the specified label (if provided)
   - Creates links to issues found by the LinkToJql query (if provided)

2. **Link Direction**:
   - **Outward Links** (default): Current issue -> Target issues
   - **Inward Links** (-LinkAsInward): Target issues -> Current issue

3. **Link Creation**: Uses Jira REST API `/rest/api/2/issueLink` endpoint to create links

## Usage Examples

### Basic Linking
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "assignee = 'old.user'" `
    -AssigneeUsername "new.user" `
    -LinkToJql "key = 'PARENT-123'" `
    -LinkType "Relates"
```

### Link to Epic Issues
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JiraIssueKey "TASK-456" `
    -AssigneeUsername "developer" `
    -LinkToJql "cf[10014] = 'EPIC-789'" `
    -LinkType "Relates" `
    -LinkComment "Part of epic coordination"
```

### Create Blocking Relationships
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "type = Bug AND priority = High" `
    -AssigneeUsername "bug.fixer" `
    -LinkToJql "priority = Critical" `
    -LinkType "Blocks" `
    -LinkAsInward
```

## Rate Limiting

The script includes rate limiting to avoid overwhelming the Jira API:
- 500ms delay between link creation calls
- 2-3 second delays between processing different issues

## Error Handling

- Links to self are automatically skipped
- Failed link creation is logged but doesn't stop processing
- Authentication is tested before any operations begin

## Limitations

- Cannot create multiple different link types in a single run
- Link creation is limited to 50 target issues per source issue
- Requires appropriate Jira permissions to create issue links

## Troubleshooting

1. **Authentication Errors**: Ensure your Personal Access Token has sufficient permissions
2. **Link Type Errors**: Verify the link type exists in your Jira configuration
3. **JQL Errors**: Test your JQL queries in Jira's issue search before using in the script
4. **Permission Errors**: Ensure you have permission to link issues in the relevant projects
