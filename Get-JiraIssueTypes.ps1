# Get-JiraIssueTypes.ps1
# Script to list all available issue types in your Jira instance

param(
    [Parameter(Mandatory=$true)]
    [string]$PersonalAccessToken,
    
    [Parameter(Mandatory=$false)]
    [string]$JiraUrl = "https://jira.softlayer.local"
)

# Create auth header with Bearer token
$headers = @{
    Authorization = "Bearer $PersonalAccessToken"
    "Content-Type" = "application/json"
}

# Test authentication first
try {
    $testUrl = "$JiraUrl/rest/api/2/myself"
    $response = Invoke-RestMethod -Uri $testUrl -Headers $headers -Method GET
    Write-Host "Authentication successful. Connected as: $($response.displayName)" -ForegroundColor Green
} catch {
    Write-Host "Authentication failed: $_" -ForegroundColor Red
    exit
}

# Get all issue types
try {
    $issueTypesUrl = "$JiraUrl/rest/api/2/issuetype"
    $issueTypes = Invoke-RestMethod -Uri $issueTypesUrl -Headers $headers -Method GET
    
    Write-Host "`nAll Issue Types:" -ForegroundColor Cyan
    $issueTypes | Format-Table id, name, description, subtask
    
    Write-Host "`nSubtask Types Only:" -ForegroundColor Green
    $subtaskTypes = $issueTypes | Where-Object { $_.subtask -eq $true }
    $subtaskTypes | Format-Table id, name, description
    
    Write-Host "`nTo use in your CSV, use either:" -ForegroundColor Yellow
    Write-Host "- Name: $($subtaskTypes[0].name)" -ForegroundColor Yellow
    Write-Host "- ID: $($subtaskTypes[0].id)" -ForegroundColor Yellow
} catch {
    Write-Host "Error retrieving issue types: $_" -ForegroundColor Red
}