# Changelog - Jira Ticket Linking Enhancement

## Version 2.0 - Issue Linking and Optional Assignment

### 🎉 Major New Features

#### Issue Linking via JQL
- **New Parameter**: `-LinkToJql` - JQL query to find issues to link to
- **New Parameter**: `-LinkType` - Type of link to create (default: "Relates")
- **New Parameter**: `-LinkComment` - Optional comment for links
- **New Parameter**: `-LinkAsInward` - Switch to create inward links

#### Flexible Operations
- **Assignment is now OPTIONAL** - `-AssigneeUsername` is no longer required
- **Link-only operations** - Create links without changing assignments
- **Label-only operations** - Add labels without assignments or links
- **Combined operations** - Any combination of assignment, labeling, and linking

### 🔧 Technical Improvements

#### New Functions
- `Get-IssuesFromJql()` - Retrieves issues using J<PERSON> with pagination
- `Add-IssueLink()` - Creates individual issue links via REST API
- `Add-LinksToIssue()` - Handles linking multiple target issues to source

#### Enhanced Validation
- Validates that at least one operation is specified
- Prevents self-linking (issue linking to itself)
- Improved error handling for link creation failures

#### Rate Limiting
- 500ms delay between link creation calls
- Maintains existing 2-3 second delays between issue processing

### 📝 Usage Examples

#### Link-Only Operation
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "token" `
    -JqlQuery "project = 'PROJ'" `
    -LinkToJql "key = 'EPIC-123'" `
    -LinkType "Relates"
```

#### Label-Only Operation
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "token" `
    -JqlQuery "status = 'Done'" `
    -Label "completed"
```

#### Combined Operations
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "token" `
    -JqlQuery "assignee = 'old.user'" `
    -AssigneeUsername "new.user" `
    -Label "reassigned" `
    -LinkToJql "key = 'PARENT-456'" `
    -LinkType "Blocks"
```

### 🔄 Backward Compatibility
- All existing functionality preserved
- Existing scripts will continue to work unchanged
- Parameter syntax remains the same for assignment operations

### 📚 Documentation
- `README-LinkingFeature.md` - Comprehensive feature documentation
- `Example-LinkTickets.ps1` - Practical usage examples
- Enhanced inline documentation and comments

### 🚀 Benefits
- **Flexibility**: Perform any combination of operations
- **Efficiency**: Batch link creation with rate limiting
- **Automation**: Link tickets based on dynamic JQL queries
- **Integration**: Works with existing Jira workflows and permissions

### ⚠️ Requirements
- Jira Personal Access Token with link creation permissions
- At least one operation must be specified (validation enforced)
- Link types must exist in your Jira configuration
