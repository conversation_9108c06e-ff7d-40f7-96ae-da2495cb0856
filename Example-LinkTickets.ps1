# Example-LinkTickets.ps1
# Examples of how to use the enhanced Assign-JiraTickets.ps1 script with linking functionality

# Example 1: Assign tickets and link them to a specific parent ticket
# This finds all tickets assigned to a specific user and links them to a parent epic
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "assignee = 'john.doe' AND status = 'In Progress'" `
    -AssigneeUsername "jane.smith" `
    -LinkToJql "key = 'PROJ-123'" `
    -LinkType "Relates" `
    -LinkComment "Automatically linked during assignment transfer"

# Example 2: Assign tickets and create inward links (target issues point to current issues)
# This assigns bug tickets and creates inward "Blocks" links to all critical issues
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "type = Bug AND priority = High" `
    -AssigneeUsername "bug.fixer" `
    -LinkToJql "priority = Critical AND status != Closed" `
    -LinkType "Blocks" `
    -LinkAsInward `
    -LinkComment "High priority bug blocks critical work"

# Example 3: Assign single ticket and link to multiple related issues
# This assigns a specific ticket and links it to all issues in the same epic
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JiraIssueKey "PROJ-456" `
    -AssigneeUsername "epic.owner" `
    -LinkToJql "cf[10014] = 'PROJ-789'" `
    -LinkType "Relates" `
    -Label "epic-coordination"

# Example 4: Assign tickets with labels and link to issues with specific labels
# This finds unassigned tickets, assigns them, adds a label, and links to all issues with 'dependency' label
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "assignee is EMPTY AND project = 'MYPROJ'" `
    -AssigneeUsername "new.developer" `
    -Label "newly-assigned" `
    -LinkToJql "labels = 'dependency' AND project = 'MYPROJ'" `
    -LinkType "Depends" `
    -LinkComment "New assignment depends on existing dependencies"

# Example 5: Link tickets without changing assignee (assign to current assignee)
# This demonstrates linking functionality by "assigning" tickets to their current assignee
# Note: You would need to modify the script to support this use case or use a different approach

Write-Host "These are example commands. Replace 'your-token-here' with your actual Jira Personal Access Token." -ForegroundColor Yellow
Write-Host "Modify the JQL queries, usernames, and other parameters according to your Jira setup." -ForegroundColor Yellow
