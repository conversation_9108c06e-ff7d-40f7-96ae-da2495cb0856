# Example-LinkTickets.ps1
# Examples of how to use the enhanced Assign-JiraTickets.ps1 script with linking functionality

# Example 1: Assign tickets and link them to a specific parent ticket
# This finds all tickets assigned to a specific user and links them to a parent epic
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "assignee = 'john.doe' AND status = 'In Progress'" `
    -AssigneeUsername "jane.smith" `
    -LinkToJql "key = 'PROJ-123'" `
    -LinkType "Relates" `
    -LinkComment "Automatically linked during assignment transfer"

# Example 2: Assign tickets and create inward links (target issues point to current issues)
# This assigns bug tickets and creates inward "Blocks" links to all critical issues
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "type = Bug AND priority = High" `
    -AssigneeUsername "bug.fixer" `
    -LinkToJql "priority = Critical AND status != Closed" `
    -LinkType "Blocks" `
    -LinkAsInward `
    -LinkComment "High priority bug blocks critical work"

# Example 3: Assign single ticket and link to multiple related issues
# This assigns a specific ticket and links it to all issues in the same epic
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JiraIssueKey "PROJ-456" `
    -AssigneeUsername "epic.owner" `
    -LinkToJql "cf[10014] = 'PROJ-789'" `
    -LinkType "Relates" `
    -Label "epic-coordination"

# Example 4: Assign tickets with labels and link to issues with specific labels
# This finds unassigned tickets, assigns them, adds a label, and links to all issues with 'dependency' label
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "assignee is EMPTY AND project = 'MYPROJ'" `
    -AssigneeUsername "new.developer" `
    -Label "newly-assigned" `
    -LinkToJql "labels = 'dependency' AND project = 'MYPROJ'" `
    -LinkType "Depends" `
    -LinkComment "New assignment depends on existing dependencies"

# Example 5: Link tickets without assignment (link-only operation)
# This creates links between tickets without changing assignments
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "project = 'MYPROJ' AND type = Story" `
    -LinkToJql "project = 'MYPROJ' AND type = Epic" `
    -LinkType "Relates" `
    -LinkComment "Stories linked to epics automatically"

# Example 6: Add labels only (no assignment or linking)
# This adds labels to tickets without changing assignments or creating links
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "status = 'In Review' AND labels is EMPTY" `
    -Label "needs-review"

# Example 7: Combined operations without assignment
# This adds labels and creates links without changing assignments
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token-here" `
    -JqlQuery "project = 'PROJ' AND fixVersion = '1.0'" `
    -Label "release-1.0" `
    -LinkToJql "key = 'PROJ-RELEASE-100'" `
    -LinkType "Relates" `
    -LinkComment "Linked to release tracking ticket"

Write-Host "These are example commands. Replace 'your-token-here' with your actual Jira Personal Access Token." -ForegroundColor Yellow
Write-Host "Modify the JQL queries, usernames, and other parameters according to your Jira setup." -ForegroundColor Yellow
Write-Host "" -ForegroundColor Yellow
Write-Host "Note: AssigneeUsername is now optional! You can perform link-only or label-only operations." -ForegroundColor Green
