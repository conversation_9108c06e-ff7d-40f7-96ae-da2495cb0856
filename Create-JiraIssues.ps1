# Create-JiraIssues.ps1
# Script to create Jira issues and subtasks from a CSV file

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$PersonalAccessToken,
    
    [Parameter(Mandatory=$true)]
    [string]$CsvFilePath,
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateSubtasks,
    
    [Parameter(Mandatory=$false)]
    [string]$JiraUrl = "https://jira.softlayer.local",
    
    [Parameter(Mandatory=$false)]
    [int]$DelaySeconds = 3
)

# Create auth header with Bearer token
$headers = @{
    Authorization = "Bearer $PersonalAccessToken"
    "Content-Type" = "application/json"
}

# Test authentication first
function Test-JiraAuth {
    try {
        $testUrl = "$JiraUrl/rest/api/2/myself"
        
        # Check if SkipHttpErrorCheck parameter exists (PowerShell 7+)
        $params = @{
            Uri = $testUrl
            Headers = $headers
            Method = "GET"
            ErrorAction = "Stop"
        }
        
        if (Get-Command Invoke-RestMethod | Select-Object -ExpandProperty Parameters | Where-Object { $_.Key -eq "SkipHttpErrorCheck" }) {
            $params.Add("SkipHttpErrorCheck", $true)
            $params.Add("StatusCodeVariable", "statusCode")
        }
        
        $response = Invoke-RestMethod @params
        
        # Check status code if we're using PowerShell 7+
        if (Get-Variable -Name statusCode -ErrorAction SilentlyContinue) {
            if ($statusCode -ge 400) {
                Write-Host "Authentication failed. Status code: $statusCode" -ForegroundColor Red
                return $false
            }
        }
        
        Write-Host "Authentication successful. Connected as: $($response.displayName)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
        
        # Display the status code if available
        if ($_.Exception.Response) {
            Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
            
            # Try to extract the response body from the error
            try {
                # For PowerShell 5.1 and earlier
                if ($_.Exception.Response.GetResponseStream) {
                    $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                    $reader.BaseStream.Position = 0
                    $reader.DiscardBufferedData()
                    $responseBody = $reader.ReadToEnd()
                }
                # For PowerShell 7+
                elseif ($_.ErrorDetails.Message) {
                    $responseBody = $_.ErrorDetails.Message
                }
                
                if ($responseBody) {
                    Write-Host "Error details: $responseBody" -ForegroundColor Red
                }
            }
            catch {
                Write-Host "Could not read error response: $_" -ForegroundColor Red
                
                # Try alternative method for PowerShell 7+
                if ($_.ErrorDetails -and $_.ErrorDetails.Message) {
                    Write-Host "Error details from ErrorDetails: $($_.ErrorDetails.Message)" -ForegroundColor Red
                }
            }
        }
        
        return $false
    }
}

# Function to get parent issue information
function Get-JiraIssue {
    param (
        [string]$IssueKey
    )
    
    $getUrl = "$JiraUrl/rest/api/2/issue/$IssueKey"
    
    try {
        $response = Invoke-RestMethod -Uri $getUrl -Headers $headers -Method GET -ErrorVariable restError -ErrorAction Stop
        Write-Host "Successfully retrieved issue: $IssueKey" -ForegroundColor Green
        return $response
    }
    catch {
        Write-Host "Error retrieving issue $IssueKey`: $($_.Exception.Message)" -ForegroundColor Red
        
        # Display the status code
        Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
        
        # Try to extract the response body from the error
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $reader.BaseStream.Position = 0
            $reader.DiscardBufferedData()
            $responseBody = $reader.ReadToEnd()
            Write-Host "Error details: $responseBody" -ForegroundColor Red
        }
        catch {
            Write-Host "Could not read error response: $_" -ForegroundColor Red
        }
        
        return $null
    }
}

# Function to search for Jira issues using JQL
function Search-JiraIssues {
    param (
        [string]$JqlQuery,
        [int]$MaxResults = 100
    )
    
    $searchUrl = "$JiraUrl/rest/api/2/search"
    $searchBody = @{
        jql = $JqlQuery
        maxResults = $MaxResults
        fields = @("key", "summary")
    } | ConvertTo-Json
    
    try {
        $searchResult = Invoke-RestMethod -Uri $searchUrl -Headers $headers -Method POST -Body $searchBody -ErrorVariable restError -ErrorAction Stop
        Write-Host "Found $($searchResult.total) issues matching query" -ForegroundColor Green
        return $searchResult.issues
    }
    catch {
        Write-Host "Error searching for issues: $($_.Exception.Message)" -ForegroundColor Red
        
        # Display the status code
        Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
        
        # Try to extract the response body from the error
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $reader.BaseStream.Position = 0
            $reader.DiscardBufferedData()
            $responseBody = $reader.ReadToEnd()
            Write-Host "Error details: $responseBody" -ForegroundColor Red
        }
        catch {
            Write-Host "Could not read error response: $_" -ForegroundColor Red
        }
        
        return @()
    }
}

# Function to find parent issue by various criteria
function Find-ParentIssue {
    param (
        [string]$ParentIdentifier,
        [string]$ProjectKey
    )
    
    # Check if it's already a valid issue key (PROJECT-123 format)
    if ($ParentIdentifier -match "^[A-Z]+-\d+$") {
        $issue = Get-JiraIssue -IssueKey $ParentIdentifier
        if ($issue) {
            return $issue.key
        }
    }
    
    # Try to search by summary if it's not a key
    $jql = "project = '$ProjectKey' AND summary ~ '$ParentIdentifier'"
    $issues = Search-JiraIssues -JqlQuery $jql -MaxResults 10
    
    if ($issues.Count -eq 0) {
        Write-Host "No parent issues found matching '$ParentIdentifier'" -ForegroundColor Yellow
        return $null
    }
    elseif ($issues.Count -eq 1) {
        Write-Host "Found parent issue: $($issues[0].key) - $($issues[0].fields.summary)" -ForegroundColor Green
        return $issues[0].key
    }
    else {
        Write-Host "Multiple potential parent issues found:" -ForegroundColor Yellow
        for ($i = 0; $i -lt [Math]::Min($issues.Count, 5); $i++) {
            Write-Host "[$i] $($issues[$i].key): $($issues[$i].fields.summary)" -ForegroundColor Cyan
        }
        
        $selection = Read-Host "Enter the number of the correct parent issue (or 'skip' to skip this subtask)"
        if ($selection -eq "skip") {
            return $null
        }
        
        $index = [int]$selection
        if ($index -ge 0 -and $index -lt $issues.Count) {
            return $issues[$index].key
        }
        else {
            Write-Host "Invalid selection. Skipping this subtask." -ForegroundColor Yellow
            return $null
        }
    }
}

# Function to create a Jira issue with improved error handling for both older and newer PowerShell versions
function New-JiraIssue {
    param (
        [hashtable]$IssueFields
    )
    
    $createUrl = "$JiraUrl/rest/api/2/issue"
    
    # Ensure labels is an array if it exists
    if ($IssueFields.ContainsKey("labels")) {
        # Force array type even if it's a single item
        $IssueFields["labels"] = @($IssueFields["labels"])
    }
    
    # Prepare the request body
    $bodyObject = @{
        fields = $IssueFields
    }
    
    # Convert to JSON with proper depth
    $body = ConvertTo-Json -InputObject $bodyObject -Depth 10
    
    # Debug output for request body
    Write-Host "Request body JSON:" -ForegroundColor Magenta
    Write-Host $body -ForegroundColor Magenta
    
    try {
        # Use SkipHttpErrorCheck to get the response even when there's an error
        $params = @{
            Uri = $createUrl
            Headers = $headers
            Method = "POST"
            Body = $body
            ErrorAction = "Stop"
            ContentType = "application/json"
        }
        
        # Check if SkipHttpErrorCheck parameter exists (PowerShell 7+)
        if (Get-Command Invoke-RestMethod | Select-Object -ExpandProperty Parameters | Where-Object { $_.Key -eq "SkipHttpErrorCheck" }) {
            $params.Add("SkipHttpErrorCheck", $true)
            $params.Add("StatusCodeVariable", "statusCode")
        }
        
        $result = Invoke-RestMethod @params
        
        # Check status code if we're using PowerShell 7+
        if (Get-Variable -Name statusCode -ErrorAction SilentlyContinue) {
            if ($statusCode -ge 400) {
                Write-Host "Error creating issue. Status code: $statusCode" -ForegroundColor Red
                Write-Host "Response: $($result | ConvertTo-Json -Depth 5)" -ForegroundColor Red
                return $null
            }
        }
        
        Write-Host "Successfully created issue: $($result.key)" -ForegroundColor Green
        return $result
    }
    catch {
        Write-Host "Error creating issue: $($_.Exception.Message)" -ForegroundColor Red
        
        # Display the status code
        if ($_.Exception.Response) {
            Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
            
            # Try to extract the response body from the error
            try {
                # For PowerShell 5.1 and earlier
                if ($_.Exception.Response.GetResponseStream) {
                    $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                    $reader.BaseStream.Position = 0
                    $reader.DiscardBufferedData()
                    $responseBody = $reader.ReadToEnd()
                }
                # For PowerShell 7+
                elseif ($_.ErrorDetails.Message) {
                    $responseBody = $_.ErrorDetails.Message
                }
                
                if ($responseBody) {
                    # Try to parse as JSON
                    try {
                        $errorObject = $responseBody | ConvertFrom-Json
                        Write-Host "Error details:" -ForegroundColor Red
                        Write-Host ($errorObject | ConvertTo-Json -Depth 5) -ForegroundColor Red
                        
                        # Display specific field errors if available
                        if ($errorObject.errors) {
                            Write-Host "Field errors:" -ForegroundColor Red
                            foreach ($field in $errorObject.errors.PSObject.Properties) {
                                Write-Host "  $($field.Name): $($field.Value)" -ForegroundColor Red
                            }
                        }
                    }
                    catch {
                        # If not valid JSON, show raw response
                        Write-Host "Raw error response:" -ForegroundColor Red
                        Write-Host $responseBody -ForegroundColor Red
                    }
                }
            }
            catch {
                Write-Host "Could not read error response: $_" -ForegroundColor Red
                
                # Try alternative method for PowerShell 7+
                if ($_.ErrorDetails -and $_.ErrorDetails.Message) {
                    Write-Host "Error details from ErrorDetails: $($_.ErrorDetails.Message)" -ForegroundColor Red
                }
            }
        }
        
        # Show the request body for debugging
        Write-Host "Request body:" -ForegroundColor Yellow
        Write-Host $body -ForegroundColor Yellow
        
        return $null
    }
}

# Function to create a subtask
function New-JiraSubtask {
    param (
        [string]$ParentKey,
        [hashtable]$SubtaskFields
    )
    
    # Add parent information to the subtask fields
    $SubtaskFields["parent"] = @{
        key = $ParentKey
    }
    
    # Debug output for subtask fields
    Write-Host "Debug - Subtask Fields:" -ForegroundColor Magenta
    Write-Host ($SubtaskFields | ConvertTo-Json -Depth 5) -ForegroundColor Magenta
    
    # Make sure issuetype is set to subtask
    if (-not $SubtaskFields.issuetype -or (-not $SubtaskFields.issuetype.id -and -not $SubtaskFields.issuetype.name)) {
        Write-Host "Warning: Subtask must have a valid issuetype. Please check your CSV file." -ForegroundColor Yellow
        return $null
    }
    
    return New-JiraIssue -IssueFields $SubtaskFields
}

# Function to convert CSV data to Jira fields with proper array handling for labels
function ConvertTo-JiraFields {
    param (
        [PSCustomObject]$CsvRow,
        [bool]$IsSubtask = $false
    )
    
    $jiraFields = @{}
    
    # Process each property in the CSV row
    foreach ($property in $CsvRow.PSObject.Properties) {
        $name = $property.Name
        $value = $property.Value
        
        # Skip empty values
        if ([string]::IsNullOrWhiteSpace($value)) {
            continue
        }
        
        # Handle special fields
        switch ($name) {
            "project" {
                if ($value -match "^\d+$") {
                    # If value is numeric, treat as project ID
                    $jiraFields[$name] = @{ id = $value }
                } else {
                    # Otherwise treat as project key
                    $jiraFields[$name] = @{ key = $value }
                }
            }
            "issuetype" {
                if ($value -match "^\d+$") {
                    # If value is numeric, treat as issuetype ID
                    $jiraFields[$name] = @{ id = $value }
                } else {
                    # Otherwise treat as issuetype name
                    $jiraFields[$name] = @{ name = $value }
                }
            }
            "priority" {
                if ($value -match "^\d+$") {
                    # If value is numeric, treat as priority ID
                    $jiraFields[$name] = @{ id = $value }
                } else {
                    # Otherwise treat as priority name
                    $jiraFields[$name] = @{ name = $value }
                }
            }
            "assignee" {
                $jiraFields[$name] = @{ name = $value }
            }
            "reporter" {
                $jiraFields[$name] = @{ name = $value }
            }
            "components" {
                # Handle multiple components separated by commas
                $componentsList = $value -split ','
                $components = @()
                foreach ($component in $componentsList) {
                    if ($component -match "^\d+$") {
                        $components += @{ id = $component.Trim() }
                    } else {
                        $components += @{ name = $component.Trim() }
                    }
                }
                $jiraFields[$name] = $components
            }
            "labels" {
                # Ensure labels is always an array
                if ([string]::IsNullOrWhiteSpace($value)) {
                    $jiraFields[$name] = @()
                } else {
                    # Split by comma and create a proper array
                    $labelArray = ($value -split ',') | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
                    $jiraFields[$name] = @($labelArray)
                }
                
                # Debug output
                Write-Host "Labels value: $($jiraFields[$name] | ConvertTo-Json)" -ForegroundColor Magenta
            }
            "customfield_" {
                # Handle custom fields
                if ($name -match "^customfield_\d+$") {
                    $jiraFields[$name] = $value
                }
            }
            "parent" {
                # Skip parent field as it's handled separately
                continue
            }
            default {
                # For standard fields like summary, description, etc.
                $jiraFields[$name] = $value
            }
        }
    }
    
    return $jiraFields
}

# Function to validate CSV format
function Test-CsvFormat {
    param (
        [string]$CsvFilePath
    )
    
    Write-Host "Validating CSV file: $CsvFilePath" -ForegroundColor Cyan
    
    # Check if file exists
    if (-not (Test-Path $CsvFilePath)) {
        Write-Host "Error: CSV file not found" -ForegroundColor Red
        return $false
    }
    
    # Read raw content to check for BOM and encoding issues
    $bytes = [System.IO.File]::ReadAllBytes($CsvFilePath)
    $hasBom = $false
    
    # Check for UTF-8 BOM
    if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
        $hasBom = $true
        Write-Host "CSV has UTF-8 BOM marker" -ForegroundColor Yellow
    }
    
    # Read the file content
    $content = Get-Content -Path $CsvFilePath -Raw
    
    # Check for common CSV issues
    if ($content -match "`r`r`n") {
        Write-Host "Warning: CSV contains double carriage returns" -ForegroundColor Yellow
    }
    
    # Check header line
    $headerLine = ($content -split "`n")[0].Trim()
    Write-Host "Header line: $headerLine" -ForegroundColor Cyan
    
    # Check for quotes in header
    if ($headerLine -match '"') {
        Write-Host "Headers contain quotes which may cause issues" -ForegroundColor Yellow
    }
    
    # Try to import the CSV
    try {
        $csv = Import-Csv -Path $CsvFilePath
        Write-Host "CSV imported successfully with $($csv.Count) rows" -ForegroundColor Green
        
        # Check first row
        if ($csv.Count -gt 0) {
            Write-Host "First row properties:" -ForegroundColor Cyan
            foreach ($prop in $csv[0].PSObject.Properties) {
                Write-Host "  $($prop.Name) = $($prop.Value)" -ForegroundColor Cyan
            }
        }
        
        return $true
    }
    catch {
        Write-Host "Error importing CSV: $_" -ForegroundColor Red
        return $false
    }
}

# Main script execution
if (Test-JiraAuth) {
    if (-not (Test-CsvFormat -CsvFilePath $CsvFilePath)) {
        Write-Host "CSV validation failed. Please fix the issues before continuing." -ForegroundColor Red
        exit
    }
    
    # Import CSV file
    try {
        $csvData = Import-Csv -Path $CsvFilePath
        Write-Host "Successfully imported CSV with $($csvData.Count) rows" -ForegroundColor Green
    }
    catch {
        Write-Host "Error importing CSV file: $_" -ForegroundColor Red
        exit
    }
    
    # Validate CSV structure
    $requiredColumns = @("project", "issuetype", "summary")
    $missingColumns = $requiredColumns | Where-Object { $_ -notin $csvData[0].PSObject.Properties.Name }
    
    if ($missingColumns.Count -gt 0) {
        Write-Host "Error: CSV is missing required columns: $($missingColumns -join ', ')" -ForegroundColor Red
        Write-Host "Required columns are: $($requiredColumns -join ', ')" -ForegroundColor Yellow
        exit
    }
    
    # Process CSV rows
    $parentIssues = @{}
    $rowCount = 0
    $totalRows = $csvData.Count
    
    foreach ($row in $csvData) {
        $rowCount++
        Write-Host "Processing row $rowCount of $totalRows" -ForegroundColor Cyan
        
        # Check if this is a subtask
        $isSubtask = $false
        $parentKey = $null
        
        if ($CreateSubtasks -and $row.PSObject.Properties.Name -contains "parent" -and -not [string]::IsNullOrWhiteSpace($row.parent)) {
            $isSubtask = $true
            $parentKey = $row.parent
        }
        
        # Convert CSV row to Jira fields
        $jiraFields = ConvertTo-JiraFields -CsvRow $row -IsSubtask $isSubtask
        
        # Create the issue
        if ($isSubtask) {
            # Check if parent exists in our created issues dictionary
            if ($parentIssues.ContainsKey($parentKey)) {
                $actualParentKey = $parentIssues[$parentKey]
                Write-Host "Creating subtask for parent issue: $actualParentKey" -ForegroundColor Cyan
                $result = New-JiraSubtask -ParentKey $actualParentKey -SubtaskFields $jiraFields
            } 
            else {
                # Try to find the parent issue in Jira
                $projectKey = $row.project
                Write-Host "Parent issue '$parentKey' not found in current batch. Searching in Jira..." -ForegroundColor Yellow
                $actualParentKey = Find-ParentIssue -ParentIdentifier $parentKey -ProjectKey $projectKey
                
                if ($actualParentKey) {
                    Write-Host "Creating subtask for parent issue: $actualParentKey" -ForegroundColor Cyan
                    $result = New-JiraSubtask -ParentKey $actualParentKey -SubtaskFields $jiraFields
                    # Add to our dictionary for future reference
                    $parentIssues[$parentKey] = $actualParentKey
                } 
                else {
                    Write-Host "Warning: Parent issue '$parentKey' not found. Skipping subtask creation." -ForegroundColor Yellow
                    continue
                }
            }
        } else {
            $result = New-JiraIssue -IssueFields $jiraFields
            
            # Store the created issue key for potential subtasks
            if ($result) {
                if ($row.PSObject.Properties.Name -contains "key" -and -not [string]::IsNullOrWhiteSpace($row.key)) {
                    $parentIssues[$row.key] = $result.key
                } else {
                    $parentIssues[$result.key] = $result.key
                }
            }
        }
        
        # Add delay between API calls to respect rate limits
        if ($rowCount -lt $totalRows) {
            Write-Host "Waiting $DelaySeconds seconds before processing next row (rate limit)..." -ForegroundColor Yellow
            Start-Sleep -Seconds $DelaySeconds
        }
    }
    
    Write-Host "Processing complete. Created issues will be available in Jira." -ForegroundColor Green
}
else {
    Write-Host "Aborting operation due to authentication failure." -ForegroundColor Red
}







