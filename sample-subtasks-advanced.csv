project,issuetype,summary,description,assignee,priority,labels,components,customfield_10001,customfield_10002,parent
PROJ,Sub-task,Frontend Development,Develop React components for user dashboard,frontend.dev,High,"frontend,react,dashboard","UI Components,Frontend","5","2024-Q1",PROJ-789
PROJ,Sub-task,Backend API Development,Create REST APIs for dashboard data,backend.dev,High,"backend,api,dashboard","API,Backend","8","2024-Q1",PROJ-789
PROJ,Sub-task,Database Schema Updates,Update database schema for new features,db.developer,Medium,"database,schema","Database,Backend","3","2024-Q1",PROJ-789
PROJ,Sub-task,Integration Testing,Test integration between frontend and backend,qa.engineer,Medium,"testing,integration","QA,Testing","5","2024-Q1",PROJ-789
PROJ,Sub-task,Performance Optimization,Optimize application performance,performance.engineer,Low,"performance,optimization","Performance,Backend","13","2024-Q2",PROJ-789
PROJ,Sub-task,Security Review,Conduct security review of new features,security.engineer,High,"security,review","Security,Compliance","8","2024-Q1",PROJ-789
