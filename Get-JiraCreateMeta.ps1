# Get-JiraCreateMeta.ps1
# Script to get metadata about required fields for issue creation

param(
    [Parameter(Mandatory=$true)]
    [string]$PersonalAccessToken,
    
    [Parameter(Mandatory=$true)]
    [string]$ProjectKey,
    
    [Parameter(Mandatory=$true)]
    [string]$IssueTypeName,
    
    [Parameter(Mandatory=$false)]
    [string]$JiraUrl = "https://jira.softlayer.local"
)

# Create auth header with Bearer token
$headers = @{
    Authorization = "Bearer $PersonalAccessToken"
    "Content-Type" = "application/json"
}

# Test authentication
try {
    $testUrl = "$JiraUrl/rest/api/2/myself"
    $response = Invoke-RestMethod -Uri $testUrl -Headers $headers -Method GET
    Write-Host "Authentication successful. Connected as: $($response.displayName)" -ForegroundColor Green
} catch {
    Write-Host "Authentication failed: $_" -ForegroundColor Red
    exit
}

# Get create metadata
try {
    $metaUrl = "$JiraUrl/rest/api/2/issue/createmeta?projectKeys=$ProjectKey&issuetypeNames=$IssueTypeName&expand=projects.issuetypes.fields"
    $meta = Invoke-RestMethod -Uri $metaUrl -Headers $headers -Method GET
    
    if ($meta.projects.Count -eq 0) {
        Write-Host "No metadata found for project $ProjectKey and issue type $IssueTypeName" -ForegroundColor Red
        exit
    }
    
    $fields = $meta.projects[0].issuetypes[0].fields
    
    Write-Host "`nRequired Fields for $IssueTypeName in $ProjectKey" -ForegroundColor Cyan
    $requiredFields = $fields.PSObject.Properties | Where-Object { $_.Value.required -eq $true }
    $requiredFields | ForEach-Object {
        Write-Host "- $($_.Name): $($_.Value.name) [$($_.Value.schema.type)]" -ForegroundColor Yellow
    }
    
    Write-Host "`nAll Available Fields:" -ForegroundColor Green
    $fields.PSObject.Properties | ForEach-Object {
        $required = if ($_.Value.required) { "[REQUIRED]" } else { "" }
        Write-Host "- $($_.Name): $($_.Value.name) $required" -ForegroundColor Cyan
    }
} catch {
    Write-Host "Error retrieving metadata: $_" -ForegroundColor Red
}